#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
物流地址审核系统 - 主脚本
支持终端拖拽Excel文件进行智能地址审核
集成DeepSeek v3.2 API + 黑名单关键词双重筛选机制
"""

import sys
import os
import asyncio
import aiohttp
import yaml
import pandas as pd
import pathlib
import re
import time
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from loguru import logger

# 跨平台彩色输出支持
try:
    import colorama
    colorama.init(autoreset=True)
    COLORS = {
        'red': colorama.Fore.RED,
        'green': colorama.Fore.GREEN,
        'yellow': colorama.Fore.YELLOW,
        'blue': colorama.Fore.BLUE,
        'cyan': colorama.Fore.CYAN,
        'magenta': colorama.Fore.MAGENTA,
        'white': colorama.Fore.WHITE,
        'reset': colorama.Style.RESET_ALL
    }
except ImportError:
    # 降级为无颜色输出
    COLORS = {k: '' for k in ['red', 'green', 'yellow', 'blue', 'cyan', 'magenta', 'white', 'reset']}

# 全局配置
CONFIG = {}
BLACKLIST_KEYWORDS = []
SEMAPHORE = None

def load_config() -> Dict:
    """加载配置文件或环境变量"""
    config = {}
    
    # 尝试加载 config.yaml
    config_path = pathlib.Path('config.yaml')
    if config_path.exists():
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
            logger.info("✅ 已加载配置文件 config.yaml")
        except Exception as e:
            logger.warning(f"⚠️ 配置文件加载失败: {e}")
    
    # 环境变量优先级更高
    if os.getenv('DEEPSEEK_API_KEY'):
        config['deepseek_api_key'] = os.getenv('DEEPSEEK_API_KEY')
    
    # 设置默认值
    defaults = {
        'max_concurrent': 20,
        'request_timeout': 30,
        'max_retries': 3,
        'retry_delay': 2,
        'prompt': '你是物流审核员，只返回两字：发货/关闭。\\n地址：{addr}',
        'output_suffix': '_已审核',
        'enable_color': True,
        'show_progress': True,
        'batch_size': 100,
        'progress_update_interval': 10
    }
    
    for key, value in defaults.items():
        if key not in config:
            config[key] = value
    
    return config

def load_blacklist() -> List[str]:
    """加载黑名单关键词"""
    blacklist_path = pathlib.Path('blacklist.txt')
    keywords = []
    
    if blacklist_path.exists():
        try:
            with open(blacklist_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#'):
                        keywords.append(line)
            logger.info(f"✅ 已加载 {len(keywords)} 个黑名单关键词")
        except Exception as e:
            logger.error(f"❌ 黑名单文件加载失败: {e}")
    else:
        logger.warning("⚠️ 未找到 blacklist.txt 文件，将跳过黑名单筛选")
    
    return keywords

def colored_print(text: str, color: str = 'white') -> None:
    """彩色输出函数"""
    if CONFIG.get('enable_color', True):
        print(f"{COLORS.get(color, '')}{text}{COLORS['reset']}")
    else:
        print(text)

def print_welcome():
    """打印欢迎信息"""
    colored_print("=" * 60, 'cyan')
    colored_print("📦 DeepSeek-Logistics-Audit v1.0", 'green')
    colored_print("🚀 智能物流地址审核系统", 'blue')
    colored_print("=" * 60, 'cyan')

def validate_file_path(file_path: pathlib.Path) -> bool:
    """验证文件路径"""
    if not file_path.exists():
        colored_print(f"❌ 文件不存在: {file_path}", 'red')
        return False
    
    if file_path.suffix.lower() not in ['.xlsx', '.xls']:
        colored_print(f"❌ 不支持的文件格式: {file_path.suffix}", 'red')
        colored_print("💡 请使用 Excel 文件 (.xlsx 或 .xls)", 'yellow')
        return False
    
    return True

def find_address_column(df: pd.DataFrame) -> Optional[str]:
    """自动识别地址列"""
    address_keywords = ['地址', '邮寄', '收货', '配送', '送货', 'address']
    
    for col in df.columns:
        col_str = str(col).lower()
        for keyword in address_keywords:
            if keyword in col_str:
                logger.info(f"✅ 自动识别地址列: {col}")
                return col
    
    # 如果没有找到，返回第一列作为默认
    if len(df.columns) > 0:
        default_col = df.columns[0]
        logger.warning(f"⚠️ 未找到明确的地址列，使用第一列: {default_col}")
        return default_col
    
    return None

def blacklist_filter(addresses: pd.Series) -> pd.Series:
    """黑名单关键词筛选"""
    if not BLACKLIST_KEYWORDS:
        return pd.Series(['发货'] * len(addresses), index=addresses.index)
    
    # 构建正则表达式模式
    pattern = '|'.join(map(re.escape, BLACKLIST_KEYWORDS))
    
    # 执行筛选
    mask = addresses.astype(str).str.contains(pattern, case=False, na=False)
    result = mask.map({True: '关闭', False: '发货'})
    
    closed_count = (result == '关闭').sum()
    colored_print(f"⚡ 黑名单初筛完成 → 关闭 {closed_count} 条", 'yellow')
    
    return result

async def deepseek_single_request(address: str, session: aiohttp.ClientSession) -> str:
    """单个地址的DeepSeek API请求"""
    async with SEMAPHORE:
        api_key = CONFIG.get('deepseek_api_key')
        if not api_key or api_key == 'sk-xxxxxxxxxxxxxxxx':
            logger.warning("⚠️ 未配置有效的DeepSeek API密钥，跳过AI复审")
            return '发货'
        
        prompt = CONFIG['prompt'].format(addr=address)
        payload = {
            "model": "deepseek-chat",
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0
        }
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        for attempt in range(1, CONFIG['max_retries'] + 1):
            try:
                timeout = aiohttp.ClientTimeout(total=CONFIG['request_timeout'])
                async with session.post(
                    "https://api.deepseek.com/v1/chat/completions",
                    json=payload,
                    headers=headers,
                    timeout=timeout
                ) as response:
                    response.raise_for_status()
                    data = await response.json()
                    content = data['choices'][0]['message']['content'].strip()
                    
                    # 解析结果
                    if '关闭' in content:
                        return '关闭'
                    else:
                        return '发货'
                        
            except Exception as e:
                logger.warning(f"DeepSeek API 请求失败 (尝试 {attempt}/{CONFIG['max_retries']}): {e}")
                if attempt < CONFIG['max_retries']:
                    await asyncio.sleep(CONFIG['retry_delay'] ** attempt)
        
        # 所有重试失败，保守返回"发货"
        return '发货'

async def deepseek_batch_review(addresses: List[str]) -> List[str]:
    """批量DeepSeek API复审"""
    if not addresses:
        return []
    
    colored_print(f"🧠 DeepSeek v3.2 复审中... 共 {len(addresses)} 条地址", 'blue')
    
    async with aiohttp.ClientSession() as session:
        tasks = [deepseek_single_request(addr, session) for addr in addresses]
        
        # 显示进度
        results = []
        batch_size = CONFIG['batch_size']
        
        for i in range(0, len(tasks), batch_size):
            batch_tasks = tasks[i:i + batch_size]
            batch_results = await asyncio.gather(*batch_tasks)
            results.extend(batch_results)
            
            # 更新进度
            progress = min(len(results), len(addresses))
            percentage = (progress / len(addresses)) * 100
            progress_bar = "█" * int(percentage / 5) + "░" * (20 - int(percentage / 5))
            colored_print(f"🧠 DeepSeek 复审进度: {progress_bar} {percentage:.1f}% ({progress}/{len(addresses)})", 'blue')
    
    return results

def generate_output_file(df: pd.DataFrame, original_path: pathlib.Path) -> pathlib.Path:
    """生成输出文件"""
    suffix = CONFIG['output_suffix']
    output_path = original_path.with_name(f"{original_path.stem}{suffix}{original_path.suffix}")
    
    try:
        df.to_excel(output_path, index=False)
        colored_print(f"🎉 结果已保存: {output_path}", 'green')
        return output_path
    except Exception as e:
        logger.error(f"❌ 文件保存失败: {e}")
        raise

def print_statistics(df: pd.DataFrame, start_time: float):
    """打印统计信息"""
    total_count = len(df)
    closed_count = (df['最终决策'] == '关闭').sum()
    shipped_count = total_count - closed_count
    closed_percentage = (closed_count / total_count) * 100 if total_count > 0 else 0
    elapsed_time = time.time() - start_time
    
    colored_print("=" * 60, 'cyan')
    colored_print("📊 审核统计结果", 'green')
    colored_print(f"📦 总订单数: {total_count}", 'white')
    colored_print(f"🚫 关闭订单: {closed_count} ({closed_percentage:.1f}%)", 'red')
    colored_print(f"✅ 发货订单: {shipped_count} ({100-closed_percentage:.1f}%)", 'green')
    colored_print(f"⏱️ 总耗时: {elapsed_time:.1f} 秒", 'blue')
    colored_print("=" * 60, 'cyan')

async def main_process(file_path: pathlib.Path):
    """主处理流程"""
    start_time = time.time()
    
    # 读取Excel文件
    colored_print(f"📖 正在读取文件: {file_path}", 'blue')
    try:
        df = pd.read_excel(file_path)
        colored_print(f"✅ 已读取 {len(df)} 条记录，用时 {time.time() - start_time:.2f} 秒", 'green')
    except Exception as e:
        colored_print(f"❌ 文件读取失败: {e}", 'red')
        return
    
    # 识别地址列
    address_col = find_address_column(df)
    if not address_col:
        colored_print("❌ 未找到地址列", 'red')
        return
    
    # 黑名单初筛
    df['初筛结果'] = blacklist_filter(df[address_col])
    
    # DeepSeek API复审
    need_llm_review = df['初筛结果'] == '发货'
    if need_llm_review.any():
        addresses_to_review = df.loc[need_llm_review, address_col].tolist()
        llm_results = await deepseek_batch_review(addresses_to_review)
        
        # 更新结果
        df.loc[need_llm_review, 'LLM复审'] = llm_results
    else:
        df['LLM复审'] = None
    
    # 生成最终决策
    df['最终决策'] = df['LLM复审'].fillna(df['初筛结果'])
    df['审核时间'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 保存结果文件
    output_path = generate_output_file(df, file_path)
    
    # 打印统计信息
    print_statistics(df, start_time)

def main():
    """主入口函数"""
    global CONFIG, BLACKLIST_KEYWORDS, SEMAPHORE
    
    # 打印欢迎信息
    print_welcome()
    
    # 检查命令行参数
    if len(sys.argv) != 2:
        colored_print("❌ 用法错误！", 'red')
        colored_print("💡 正确用法: python audit.py <Excel文件路径>", 'yellow')
        colored_print("💡 或者直接拖拽Excel文件到终端", 'yellow')
        sys.exit(1)
    
    # 解析文件路径
    file_path = pathlib.Path(sys.argv[1]).resolve()
    
    # 验证文件
    if not validate_file_path(file_path):
        sys.exit(1)
    
    # 加载配置
    CONFIG = load_config()
    BLACKLIST_KEYWORDS = load_blacklist()
    SEMAPHORE = asyncio.Semaphore(CONFIG['max_concurrent'])
    
    # 执行主处理流程
    try:
        asyncio.run(main_process(file_path))
    except KeyboardInterrupt:
        colored_print("\\n⚠️ 用户中断操作", 'yellow')
    except Exception as e:
        colored_print(f"❌ 处理过程中发生错误: {e}", 'red')
        logger.exception("详细错误信息:")

if __name__ == "__main__":
    main()