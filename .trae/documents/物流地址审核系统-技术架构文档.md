# 物流地址审核系统 - 技术架构文档

## 1. Architecture design

```mermaid
graph TD
    A[用户终端] --> B[audit.py 主脚本]
    B --> C[文件处理模块]
    B --> D[黑名单筛选模块]
    B --> E[DeepSeek API客户端]
    B --> F[配置管理模块]
    B --> G[终端输出模块]
    
    C --> H[pandas Excel处理]
    D --> I[blacklist.txt]
    E --> J[DeepSeek v3.2 API]
    F --> K[config.yaml]
    F --> L[环境变量]
    
    subgraph "单文件架构"
        B
        C
        D
        E
        F
        G
    end
    
    subgraph "配置文件"
        I
        K
    end
    
    subgraph "外部服务"
        J
    end
    
    subgraph "数据处理"
        H
    end
```

## 2. Technology Description

- Frontend: 无前端界面，纯终端交互
- Backend: Python 3.9+ 单文件脚本
- 核心依赖: pandas, aiohttp, asyncio, pathlib, loguru, colorama, pyyaml
- API服务: DeepSeek v3.2 Chat Completions API
- 跨平台: macOS/Windows 兼容性支持

## 3. Route definitions

| Route | Purpose |
|-------|---------|
| audit.py [file_path] | 主入口，接收拖拽的Excel文件路径进行审核处理 |
| config.yaml | 可选配置文件，设置API密钥、并发参数、提示词模板 |
| blacklist.txt | 黑名单关键词库，用于初步地址筛选 |

## 4. API definitions

### 4.1 Core API

DeepSeek v3.2 地址审核API
```
POST https://api.deepseek.com/v1/chat/completions
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| model | string | true | 模型名称，固定为 "deepseek-chat" |
| messages | array | true | 对话消息数组，包含审核提示词和地址信息 |
| temperature | number | true | 生成随机性，设为0确保结果一致性 |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| choices | array | API返回的选择结果数组 |
| choices[0].message.content | string | AI审核结果："发货"或"关闭" |

Example Request:
```json
{
  "model": "deepseek-chat",
  "messages": [
    {
      "role": "user", 
      "content": "你是物流审核员，只返回两字：发货/关闭。\n地址：北京市朝阳区xxx街道"
    }
  ],
  "temperature": 0
}
```

Example Response:
```json
{
  "choices": [
    {
      "message": {
        "content": "发货"
      }
    }
  ]
}
```

## 5. Server architecture diagram

```mermaid
graph TD
    A[终端输入层] --> B[参数解析层]
    B --> C[文件读取层]
    C --> D[数据处理层]
    D --> E[黑名单筛选层]
    E --> F[异步API调用层]
    F --> G[结果汇总层]
    G --> H[文件输出层]
    H --> I[统计报告层]

    subgraph "单文件脚本架构"
        B
        C
        D
        E
        F
        G
        H
        I
    end
    
    subgraph "外部依赖"
        J[pandas]
        K[aiohttp]
        L[asyncio]
        M[pathlib]
    end
    
    D --> J
    F --> K
    F --> L
    C --> M
```

## 6. Data model

### 6.1 Data model definition

```mermaid
erDiagram
    EXCEL_DATA ||--o{ ADDRESS_RECORD : contains
    ADDRESS_RECORD ||--|| BLACKLIST_RESULT : has
    ADDRESS_RECORD ||--o| LLM_RESULT : has
    ADDRESS_RECORD ||--|| FINAL_RESULT : produces

    EXCEL_DATA {
        string file_path
        int total_records
        string address_column
        datetime process_time
    }
    
    ADDRESS_RECORD {
        int index
        string address
        string original_data
    }
    
    BLACKLIST_RESULT {
        string status
        string matched_keywords
        datetime check_time
    }
    
    LLM_RESULT {
        string status
        string api_response
        int retry_count
        datetime process_time
    }
    
    FINAL_RESULT {
        string decision
        string reason
        datetime final_time
    }
```

### 6.2 Data Definition Language

配置文件结构 (config.yaml)
```yaml
# API配置
deepseek_api_key: sk-xxxxxxxxxxxxxxxx
max_concurrent: 20

# 审核提示词模板
prompt: |
  你是物流审核员，只返回两字：发货/关闭。
  地址：{addr}

# 重试配置
max_retries: 3
retry_delay: 2

# 输出配置
output_suffix: "_已审核"
enable_color: true
```

黑名单关键词文件 (blacklist.txt)
```
# 地址黑名单关键词，每行一个
无法配送
拒收
偏远地区
山区
海岛
军事区域
监狱
看守所
```

Excel数据处理结构
```python
# 输入Excel列结构
columns = [
    "订单号",      # 订单唯一标识
    "收货人",      # 收货人姓名
    "邮寄地址",    # 待审核的地址字段
    "联系电话",    # 联系方式
    "商品信息"     # 商品详情
]

# 输出Excel新增列
new_columns = [
    "初筛结果",    # 黑名单筛选结果：发货/关闭
    "LLM复审",     # DeepSeek API审核结果：发货/关闭
    "最终决策",    # 综合决策结果：发货/关闭
    "审核时间"     # 处理时间戳
]
```

异步处理数据结构
```python
# 并发控制配置
SEMAPHORE_LIMIT = 20  # 最大并发数
REQUEST_TIMEOUT = 30  # 请求超时时间（秒）
RETRY_BACKOFF = [1, 2, 4]  # 重试间隔（秒）

# API请求批次管理
batch_size = 100  # 每批处理的地址数量
progress_update_interval = 10  # 进度更新间隔
```