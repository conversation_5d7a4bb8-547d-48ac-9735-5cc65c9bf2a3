# 物流地址审核系统 - 产品需求文档

## 1. Product Overview

一个基于终端拖拽操作的智能物流地址审核系统，通过黑名单关键词初筛和DeepSeek v3.2 AI语义复审的双重机制，实现高效准确的地址筛选和订单处理决策。

- 解决物流行业中大批量地址审核效率低下的问题，为物流运营人员提供自动化的地址筛选工具，显著提升订单处理速度和准确性。
- 目标市场价值：帮助物流企业降低人工审核成本，提高订单处理效率，减少因地址问题导致的配送失败率。

## 2. Core Features

### 2.1 User Roles

| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| 物流运营人员 | 直接使用，无需注册 | 可拖拽Excel文件进行地址审核，查看审核结果和统计数据 |

### 2.2 Feature Module

我们的物流地址审核系统包含以下核心页面：

1. **终端交互界面**：文件拖拽识别、实时进度显示、彩色状态提示、结果统计输出
2. **配置管理界面**：API密钥配置、并发参数设置、黑名单关键词管理、审核提示词自定义

### 2.3 Page Details

| Page Name | Module Name | Feature description |
|-----------|-------------|---------------------|
| 终端交互界面 | 文件拖拽处理 | 自动识别拖拽的Excel文件路径，支持中文和空格路径处理 |
| 终端交互界面 | 数据读取显示 | 读取Excel文件并显示订单总数，自动识别地址列 |
| 终端交互界面 | 黑名单初筛 | 基于关键词库进行快速筛选，显示初筛结果统计 |
| 终端交互界面 | AI复审处理 | 调用DeepSeek API对初筛通过的地址进行语义审核 |
| 终端交互界面 | 进度显示 | 实时显示AI复审进度条和处理状态 |
| 终端交互界面 | 结果输出 | 生成带审核标签的新Excel文件，显示最终统计数据 |
| 配置管理界面 | API配置 | 设置DeepSeek API密钥和请求参数 |
| 配置管理界面 | 并发控制 | 配置最大并发数和请求限速参数 |
| 配置管理界面 | 黑名单管理 | 编辑和维护地址关键词黑名单 |
| 配置管理界面 | 提示词设置 | 自定义AI审核的提示词模板 |

## 3. Core Process

**主要用户操作流程：**

1. 用户打开终端（macOS Terminal 或 Windows CMD/PowerShell）
2. 将Excel订单文件拖拽到终端中，系统自动补全文件路径
3. 执行审核命令，系统开始处理：
   - 读取Excel文件并识别地址列
   - 使用黑名单关键词进行初步筛选
   - 对初筛通过的地址调用DeepSeek API进行语义复审
   - 实时显示处理进度和状态
4. 系统输出审核结果文件和统计摘要
5. 用户查看生成的带标签Excel文件和终端统计信息

```mermaid
graph TD
    A[打开终端] --> B[拖拽Excel文件]
    B --> C[执行审核命令]
    C --> D[读取文件数据]
    D --> E[黑名单初筛]
    E --> F{是否需要AI复审}
    F -->|是| G[DeepSeek API复审]
    F -->|否| H[生成结果文件]
    G --> I[显示复审进度]
    I --> H[生成结果文件]
    H --> J[输出统计摘要]
    J --> K[完成审核]
```

## 4. User Interface Design

### 4.1 Design Style

- **主色调**：终端黑色背景 (#000000)，绿色成功提示 (#00FF00)，红色错误提示 (#FF0000)，黄色警告 (#FFFF00)，蓝色信息 (#00BFFF)
- **按钮样式**：无按钮界面，纯终端文本交互
- **字体**：等宽字体（Consolas, Monaco, 'Courier New'），主要字号 12-14px
- **布局风格**：垂直流式布局，基于终端行输出
- **图标样式**：使用Unicode Emoji符号（📦 ✅ ❌ ⚡ 🧠 🎉 📊）

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| 终端交互界面 | 欢迎信息 | 彩色标题显示，版本号，使用说明提示 |
| 终端交互界面 | 文件处理状态 | 文件路径显示，读取进度，数据量统计 |
| 终端交互界面 | 筛选进度 | 黑名单筛选结果，通过/关闭数量统计 |
| 终端交互界面 | AI复审进度 | 实时进度条，百分比显示，处理速度 |
| 终端交互界面 | 结果摘要 | 最终统计数据，文件保存路径，总耗时 |
| 配置管理界面 | YAML配置 | 结构化配置文件，注释说明，参数验证 |

### 4.3 Responsiveness

系统主要面向桌面终端环境，支持macOS和Windows平台的原生终端应用，自动适配不同终端的彩色输出能力，在不支持ANSI颜色的环境中自动降级为纯文本输出。