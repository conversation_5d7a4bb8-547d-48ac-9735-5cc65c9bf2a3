# 物流地址审核系统配置文件
# 如果不想使用配置文件，可以设置环境变量 DEEPSEEK_API_KEY

# DeepSeek API 配置
deepseek_api_key: "sk-xxxxxxxxxxxxxxxx"  # 请替换为您的实际API密钥

# 并发控制配置
max_concurrent: 20          # 最大并发请求数（建议不超过30）
request_timeout: 30         # 单个请求超时时间（秒）
max_retries: 3             # 最大重试次数
retry_delay: 2             # 重试间隔基数（秒，使用指数退避）

# AI审核提示词模板
prompt: |
  你是物流审核员，只返回两字：发货/关闭。
  地址：{addr}

# 输出配置
output_suffix: "_已审核"    # 输出文件后缀
enable_color: true         # 是否启用彩色输出
show_progress: true        # 是否显示进度条

# 批处理配置
batch_size: 100           # 每批处理的地址数量
progress_update_interval: 10  # 进度更新间隔（处理多少条更新一次）